<!-- 回收站文案预览模态框内容 -->
<style>
.recycle-modal .content-area {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.recycle-modal .content-title {
    color: #495057;
    font-weight: 600;
    font-size: 22px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.recycle-modal .content-text {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
    line-height: 1.8;
    font-size: 15px;
    color: #495057;
    white-space: pre-line;
    word-wrap: break-word;
    text-align: left;
    margin-bottom: 25px;
}

.recycle-modal .right-panel {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e9ecef;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.recycle-modal .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recycle-modal .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.recycle-modal .badge {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 20px;
}

.recycle-modal .location-info {
    color: #dc3545;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recycle-modal .user-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.recycle-modal .info-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.recycle-modal .info-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.recycle-modal .basic-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.recycle-modal .status-section {
    margin-bottom: 20px;
}

.recycle-modal .status-item {
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.recycle-modal .status-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* 图片预览样式 */
.recycle-modal .image-grid-right {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.recycle-modal .image-item-right {
    position: relative;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
    cursor: pointer;
    transition: all 0.2s ease;
}

.recycle-modal .image-item-right:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.recycle-modal .image-thumbnail-right {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recycle-modal .image-order-right {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

.recycle-modal .deleted-badge {
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}
</style>

<div class="recycle-modal">
<div class="row">
    <!-- 左侧：标题 + 内容 -->
    <div class="col-md-8">
        <div class="content-area">
            <!-- 文案标题 -->
            <h4 class="content-title">
                <i class="bi bi-file-text text-danger"></i>
                {{ content.title }}
                <span class="deleted-badge">已删除</span>
            </h4>

            <!-- 文案内容 -->
            <div class="content-text">{{ content.content|trim }}</div>
        </div>
    </div>

    <!-- 右侧：图片 + 话题标签 + @用户 + 位置信息 + 状态 -->
    <div class="col-md-4">
        <div class="right-panel">
            <!-- 配图区域 -->
            {% if images %}
            <div class="images-section">
                <h6 class="section-title">
                    <i class="bi bi-images text-success"></i>
                    配图预览 ({{ images|length }} 张)
                </h6>
                <div class="image-grid-right">
                    {% for image in images %}
                    <div class="image-item-right"
                         onclick="showImageModal('{{ url_for('static', filename='uploads/' + image.image_path) }}', '{{ image.original_name }}')"
                         style="cursor: pointer;">
                        <img src="{{ url_for('static', filename='uploads/' + image.thumbnail_path) }}"
                             class="image-thumbnail-right"
                             title="{{ image.original_name }}"
                             alt="{{ image.original_name }}">
                        <div class="image-order-right">{{ loop.index }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- 话题标签 -->
            <div class="info-section">
                <h6 class="section-title">
                    <i class="bi bi-tags text-primary"></i>
                    话题标签
                </h6>
                <div class="tags-container">
                    {% if content.topics_list %}
                        {% for topic in content.topics_list %}
                            <span class="badge bg-primary">#{{ topic }}</span>
                        {% endfor %}
                    {% else %}
                        <span class="text-muted">暂无话题标签</span>
                    {% endif %}
                </div>
            </div>

            <!-- @用户信息 -->
            <div class="info-section">
                <h6 class="section-title">
                    <i class="bi bi-person text-info"></i>
                    @用户
                </h6>
                <div class="user-info">
                    {% if content.at_users_list %}
                        <div class="tags-container">
                            {% for user in content.at_users_list %}
                                <span class="badge bg-info">@{{ user }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="text-muted">暂无@用户</span>
                    {% endif %}
                </div>
            </div>

            <!-- 位置信息 -->
            <div class="info-section">
                <h6 class="section-title">
                    <i class="bi bi-geo-alt text-danger"></i>
                    位置信息
                </h6>
                <div class="location-info">
                    <i class="bi bi-geo-alt-fill"></i>
                    <span>{{ content.location if content.location else '暂无位置信息' }}</span>
                </div>
            </div>

            <!-- 客户和创建时间信息 -->
            <div class="info-section">
                <h6 class="section-title">
                    <i class="bi bi-info-circle text-secondary"></i>
                    基本信息
                </h6>
                <div class="basic-info">
                    <div class="mb-2">
                        <span class="badge bg-primary">{{ content.client.name if content.client else '未知客户' }}</span>
                    </div>
                    <div>
                        <span class="text-muted small">
                            创建时间：{{ content.created_at.strftime('%Y-%m-%d %H:%M') if content.created_at else '未知时间' }}
                        </span>
                    </div>
                    {% if content.deleted_at %}
                    <div>
                        <span class="text-danger small">
                            删除时间：{{ content.deleted_at.strftime('%Y-%m-%d %H:%M') }}
                        </span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 状态信息 -->
            <div class="status-section">
                <h6 class="section-title">
                    <i class="bi bi-info-circle text-danger"></i>
                    状态信息
                </h6>
                <div class="status-item">
                    <strong>当前状态：</strong>
                    <span class="badge bg-danger">已删除</span>
                </div>
                <div class="status-item">
                    <strong>删除前状态：</strong>
                    {% if content.workflow_status == 'draft' %}
                        <span class="badge bg-secondary">草稿</span>
                    {% elif content.workflow_status == 'pending_review' %}
                        <span class="badge bg-warning">待初审</span>
                    {% elif content.workflow_status == 'first_reviewed' %}
                        <span class="badge bg-success">初审通过</span>
                    {% elif content.workflow_status == 'pending_image' %}
                        <span class="badge bg-info">待上传图片</span>
                    {% elif content.workflow_status == 'image_uploaded' %}
                        <span class="badge bg-info">图片已上传</span>
                    {% elif content.workflow_status == 'pending_final_review' %}
                        <span class="badge bg-warning">待最终审核</span>
                    {% elif content.workflow_status == 'final_review' %}
                        <span class="badge bg-warning">最终审核</span>
                    {% elif content.workflow_status == 'pending_client_review' %}
                        <span class="badge bg-primary">客户待审核</span>
                    {% elif content.workflow_status == 'client_approved' %}
                        <span class="badge bg-success">客户已通过</span>
                    {% elif content.workflow_status == 'client_rejected' %}
                        <span class="badge bg-danger">客户已拒绝</span>
                    {% elif content.workflow_status == 'ready_to_publish' %}
                        <span class="badge bg-info">待发布</span>
                    {% elif content.workflow_status == 'pending_publish' %}
                        <span class="badge bg-info">待发布</span>
                    {% elif content.workflow_status == 'publishing' %}
                        <span class="badge bg-primary">发布中</span>
                    {% elif content.workflow_status == 'published' %}
                        <span class="badge bg-success">已发布</span>
                    {% elif content.workflow_status == 'publish_failed' %}
                        <span class="badge bg-danger">发布失败</span>
                    {% elif content.workflow_status == 'publish_timeout' %}
                        <span class="badge bg-warning">发布超时</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ content.workflow_status }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<script>
// 回收站预览模态框已加载
console.log('回收站预览模态框已加载');
</script>
